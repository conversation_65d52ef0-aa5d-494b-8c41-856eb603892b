# Gen AI Market Activation Use Case Form
**文件類型**: DOCX
**提取時間**: 2025-06-06 16:00:05

Generative AI Use Case Review (v1.0 OCT 2023)  – <insert Project name>

# Please note this is a global template which may be further adapted by Geo/MF risk teams. Before using this form, please check with your local QRM team to understand and follow local requirements.

# Guidance

The purpose of this form is to gather information on a proposed Generative AI Use Case to help support initial triage reviews with QRM (e.g. Clearing House) and subsequent risk evaluation. The form should be populated by the business team who owns the Use Case and reviewed by the appropriate risk stakeholders. This is an interim approach to help support consistency and easier cross-border technology transfer. (Long term approach/framework TBC)

Please ensure you are familiar with Generative AI guidance [link]. <Note to Geo/MF risk teams – insert link to your local guidance before operationalizing>

# Use Case Overview

# Triage – Project/Application Background

Client Details (if applicable – this section is for MF/Geo use in Clearing House/Review Boards etc)

About Deloitte

Deloitte refers to one or more of Deloitte Touche Tohmatsu Limited (DTTL), its global network of member firms, and their related entities (collectively, the “Deloitte organization”). DTTL (also referred to as “Deloitte Global”) and each of its member firms and related entities are legally separate and independent entities, which cannot obligate or bind each other in respect of third parties. DTTL and each DTTL member firm and related entity is liable only for its own acts and omissions, and not those of each other. DTTL does not provide services to clients. Please see www.deloitte.com/about to learn more.

## 表格內容

### 表格 1

| Part 1 Use Case Context (This section is illustrative only and should be configured as required to meet local process requirements.) | Part 1 Use Case Context (This section is illustrative only and should be configured as required to meet local process requirements.) |
| --- | --- |
| Proposed Services | Proposed Services |
| Describe the general scope and objectives of the project and the services that will be delivered while underlining the benefits of using the Gen AI tool. |  |
| Timeframe | <Please provide expected start date and timeline – e.g. dd/mm/yy for x weeks> |
| What stage of the project lifecycle are you currently in? | Date: <enter the date you have completed this form>
Project lifecycle state: <ideating, preparing vendor RFP, client contracting, preparing go-live, etc.> |

### 表格 2

| Ideation | Ideation | Ideation |
| --- | --- | --- |
| Requestor Name | Requestor Name | <Please provide your name> |
| PPMD Sponsor Name | PPMD Sponsor Name | <Please provide partner sponsor’s name and confirm they have approved this request > |
| Please provide the name of the Gen AI SME consulted regarding this use case | Please provide the name of the Gen AI SME consulted regarding this use case | <Please provide name, email address and any recommendations provided > |
| Which use case statement best fits your scenario? | Which use case statement best fits your scenario? | Which use case statement best fits your scenario? |
| I want to experiment with Gen AI to learn more about its capabilities | I want to experiment with Gen AI to learn more about its capabilities |  |
| I want to use Gen AI to transform the way Deloitte works internally | I want to use Gen AI to transform the way Deloitte works internally |  |
| I want to use Gen AI to develop a Deloitte offering | I want to use Gen AI to develop a Deloitte offering |  |
| I want to use Gen AI to develop or support development of a bid response or proposal (e.g., to generate content for a presentation) | I want to use Gen AI to develop or support development of a bid response or proposal (e.g., to generate content for a presentation) |  |
| I want to use Gen AI to enhance existing engagement delivery at a client | I want to use Gen AI to enhance existing engagement delivery at a client |  |
| I want to use Gen AI to enhance new engagement delivery at a client | I want to use Gen AI to enhance new engagement delivery at a client |  |
| Other (please specify) | Other (please specify) |  |
| About the Generative AI Solution | About the Generative AI Solution | About the Generative AI Solution |
| About the technology and solution | About the technology and solution | About the technology and solution |
| What Gen AI technology will be used? E.g. GPT-4, Llama2, Google Vertex etc. | <Please be as specific as possible on both the technology and the version> | <Please be as specific as possible on both the technology and the version> |
| What agreement is the Gen AI technology, and any associated data, provided under? | <Please be as specific as possible. Licensing could be client owned agreement, Deloitte enterprise agreement, public agreement/T&Cs that permit commercial use or Open Source based such as permissive Apache 2.0 > | <Please be as specific as possible. Licensing could be client owned agreement, Deloitte enterprise agreement, public agreement/T&Cs that permit commercial use or Open Source based such as permissive Apache 2.0 > |
| What are the inputs to the Gen AI solution?
(Training data, fine tuning, prompts? Modality (text, images etc.)) | <Please be as specific as possible on the inputs, e.g. text files or HTML pages.> | <Please be as specific as possible on the inputs, e.g. text files or HTML pages.> |
| What is the output produced and how will it be used? | < insert answer here> | < insert answer here> |
| Will you use an API or prompts to interact with the Gen AI solution? | <insert answer here> | <insert answer here> |
| Where will the Gen AI technology and solution be hosted? | Select all that apply
 Deloitte Environment  Client environment   3rd party environment | Select all that apply
 Deloitte Environment  Client environment   3rd party environment |
| How will the Gen AI technology and solution be accessed? | Select all that apply             
 Deloitte Standard Laptop   Deloitte Laptop using Client VPN  Client laptop   Deloitte Mac     Cell/Mobile    Other – please specify | Select all that apply             
 Deloitte Standard Laptop   Deloitte Laptop using Client VPN  Client laptop   Deloitte Mac     Cell/Mobile    Other – please specify |
| About the data | About the data | About the data |
| Which data types are used as input data to train / fine-tune / prompt the model? Please consult QRM if unsure | Select all that apply.   Public data  Deloitte Confidential data  Client Confidential data  High Risk Confidential data   Personal data (any type including publicly available),  Third party data vendor/provider | Select all that apply.   Public data  Deloitte Confidential data  Client Confidential data  High Risk Confidential data   Personal data (any type including publicly available),  Third party data vendor/provider |
| What permissions or constraints exist with regard to the data in scope and is the intended use aligned with these requirements (e.g. contractual terms and conditions, license agreements)? Please consult QRM if unsure | <insert answer here> | <insert answer here> |
| Who owns the input data that is used to train / fine-tune / prompt the model? | <insert answer here> | <insert answer here> |
| In which geographic location(s) will the data be processed, stored, or accessed from? | <insert answer here> | <insert answer here> |
| For how long will the input and output data be retained and by whom? | How long and by whom will the input data be retained? (Training data? Prompts?) <insert answer here>
How long and by whom will the output data be retained? <insert answer here> | How long and by whom will the input data be retained? (Training data? Prompts?) <insert answer here>
How long and by whom will the output data be retained? <insert answer here> |
| What mitigations are in place to avoid infringing intellectual property rights (Deloitte’s, clients, and/or third parties)? | <Insert mitigations in place to avoid infringing third-party IP rights and how Deloitte will retain ownership of the IP in the solution and outputs> | <Insert mitigations in place to avoid infringing third-party IP rights and how Deloitte will retain ownership of the IP in the solution and outputs> |
| Who is it intended will own the output produced by the solution? | <insert answer here> | <insert answer here> |
| About the vendor / provider of the GenAI | About the vendor / provider of the GenAI | About the vendor / provider of the GenAI |
| Who owns the Gen AI vendor / provider relationship? | Deloitte  Client  Other third party <please explain below> | Deloitte  Client  Other third party <please explain below> |
| Are there any third parties involved in the work or the solution? | <insert answer here> | <insert answer here> |
| Are the services or outputs produced by the GenAI going to be relied upon by or provided to any third party? If yes, how will the third parties use those outputs? | <insert answer here> | <insert answer here> |
| Are there existing Guardrails for this GenAI/platform and is the use case consistent with these requirements? | <insert answer here> | <insert answer here> |
| What is the cost associated with use of the GenAI and who has approved this spend? | <insert answer here> | <insert answer here> |
| About the client contract (if applicable) | About the client contract (if applicable) | About the client contract (if applicable) |
| What are the Deloitte/client facing contracts and terms applicable to the services? | <Please list any contract(s) with the client/any relevant third parties and attach to request> | <Please list any contract(s) with the client/any relevant third parties and attach to request> |

### 表格 3

| Client details | Client details |
| --- | --- |
| Client name / Internal use details | <Insert client name or details of internal use> |
| Business and Industry | <Insert industry & Service/Business Line – e.g. Government and Public Services / Consulting> |
| Conflict Check / Record ID (if applicable) | <Insert Conflict Check code(s) – e.g. UK0XXXXXX and Record ID code> |
| Reference number for client acceptance process |  |

### 表格 4

| Team | Team |
| --- | --- |
| Lead Client Service Partner | <Insert name here> |
| Lead Engagement Partner(s) | <Insert name(s) here> |
| Quality Assurance Partner | <Insert name here> |
| Engagement Lead | <Insert name here> |
| Industry Representative | <Insert name here> |
| Other interested parties (internal / external) | <Insert name here, e.g. relevant QRM, OGC Team member> |
| Gen AI SMA consulted | <Insert name here> |

### 表格 5

| Financial analysis | Financial analysis | Financial analysis |
| --- | --- | --- |
| Fee basis | <Insert client fee basis – e.g. Time and Materials, who is paying GenAI provider etc> | <Insert client fee basis – e.g. Time and Materials, who is paying GenAI provider etc> |
| Proposed fees | <Insert value – e.g. £1m> | <Insert value – e.g. £1m> |
| Proposed Discount Rate | <Insert rates – e.g. X%> | <Insert rates – e.g. X%> |
| Invoicing and payment terms | <Insert details – e.g. interim invoices, payments on account, monthly billing etc> | <Insert details – e.g. interim invoices, payments on account, monthly billing etc> |
| Value/Cost delta | <Insert answer here – Given the nature of Gen AI, could the value that Deloitte brings be called into question?> | <Insert answer here – Given the nature of Gen AI, could the value that Deloitte brings be called into question?> |
| Part 2 Generative AI Risk Factors (consider the following principles from Deloitte’s Trustworthy AI and Technology, Trust and Ethics Frameworks to identify and consider relevant risks and mitigations for the use case) | Part 2 Generative AI Risk Factors (consider the following principles from Deloitte’s Trustworthy AI and Technology, Trust and Ethics Frameworks to identify and consider relevant risks and mitigations for the use case) | Part 2 Generative AI Risk Factors (consider the following principles from Deloitte’s Trustworthy AI and Technology, Trust and Ethics Frameworks to identify and consider relevant risks and mitigations for the use case) |
| Trustworthy AI Dimensions and Considerations | Trustworthy AI Dimensions and Considerations | Response |
| Trustworthy and Ethical Technology Risks – identification of Stakeholders
The number and breadth of stakeholders may depend entirely on the envisaged use case(s). When answering this question, consider and list stakeholders at both an individual and group level:

Direct stakeholders including types of client / operational users as well as end-users, and the task allocation between the AI system and humans for meaningful interactions and appropriate human oversight and control 
Indirect stakeholders are those who may be otherwise impacted materially, physically, psychologically, or in any other way. Indirect stakeholders could include client management, regulators, client customers, relatives of client customers / users where the use case has a financial or other legal impact on customers / users, sections of society impacted by or interested in the outcomes, etc. | Trustworthy and Ethical Technology Risks – identification of Stakeholders
The number and breadth of stakeholders may depend entirely on the envisaged use case(s). When answering this question, consider and list stakeholders at both an individual and group level:

Direct stakeholders including types of client / operational users as well as end-users, and the task allocation between the AI system and humans for meaningful interactions and appropriate human oversight and control 
Indirect stakeholders are those who may be otherwise impacted materially, physically, psychologically, or in any other way. Indirect stakeholders could include client management, regulators, client customers, relatives of client customers / users where the use case has a financial or other legal impact on customers / users, sections of society impacted by or interested in the outcomes, etc. | Which stakeholder groups are impacted by the outcomes of the Gen AI solution?

Response: |
| Accountable
The internal use of Gen AI and the delivery of Gen AI services to clients can be complex and requires effective governance and oversight, including consideration of business value, legal and regulatory obligations, data use, human intervention (or lack thereof), system integrations, and potential for harm. 
Put an organizational structure and policies in place that can help clearly determine who is responsible for the output of an AI system and decisions made or derived.

Human oversight and escalation: Humans can and should act as an effective governance mechanism in a technology’s decision making
Ownership: Accountability between developers, organizations, users, and other stakeholders is established and clearly outlined for the technology and its outputs / decisions. We have a structure and policies in place that help to clearly determine who is responsible if something was to go wrong, or if outcomes are not used as intended, and we understand the redress mechanisms 
Resolvable: Unforeseen concerns and mistakes by the technology can be resolved in a timely manner

Ensure accountability is clearly articulated and implemented throughout the system life cycle. | Accountable
The internal use of Gen AI and the delivery of Gen AI services to clients can be complex and requires effective governance and oversight, including consideration of business value, legal and regulatory obligations, data use, human intervention (or lack thereof), system integrations, and potential for harm. 
Put an organizational structure and policies in place that can help clearly determine who is responsible for the output of an AI system and decisions made or derived.

Human oversight and escalation: Humans can and should act as an effective governance mechanism in a technology’s decision making
Ownership: Accountability between developers, organizations, users, and other stakeholders is established and clearly outlined for the technology and its outputs / decisions. We have a structure and policies in place that help to clearly determine who is responsible if something was to go wrong, or if outcomes are not used as intended, and we understand the redress mechanisms 
Resolvable: Unforeseen concerns and mistakes by the technology can be resolved in a timely manner

Ensure accountability is clearly articulated and implemented throughout the system life cycle. | Please explain accountability responsibilities for the system including who (e.g., user personas) is accountable for decisions made by the system, output, and quality throughout the lifecycle.

Response: |
| Accountable
The internal use of Gen AI and the delivery of Gen AI services to clients can be complex and requires effective governance and oversight, including consideration of business value, legal and regulatory obligations, data use, human intervention (or lack thereof), system integrations, and potential for harm. 
Put an organizational structure and policies in place that can help clearly determine who is responsible for the output of an AI system and decisions made or derived.

Human oversight and escalation: Humans can and should act as an effective governance mechanism in a technology’s decision making
Ownership: Accountability between developers, organizations, users, and other stakeholders is established and clearly outlined for the technology and its outputs / decisions. We have a structure and policies in place that help to clearly determine who is responsible if something was to go wrong, or if outcomes are not used as intended, and we understand the redress mechanisms 
Resolvable: Unforeseen concerns and mistakes by the technology can be resolved in a timely manner

Ensure accountability is clearly articulated and implemented throughout the system life cycle. | Accountable
The internal use of Gen AI and the delivery of Gen AI services to clients can be complex and requires effective governance and oversight, including consideration of business value, legal and regulatory obligations, data use, human intervention (or lack thereof), system integrations, and potential for harm. 
Put an organizational structure and policies in place that can help clearly determine who is responsible for the output of an AI system and decisions made or derived.

Human oversight and escalation: Humans can and should act as an effective governance mechanism in a technology’s decision making
Ownership: Accountability between developers, organizations, users, and other stakeholders is established and clearly outlined for the technology and its outputs / decisions. We have a structure and policies in place that help to clearly determine who is responsible if something was to go wrong, or if outcomes are not used as intended, and we understand the redress mechanisms 
Resolvable: Unforeseen concerns and mistakes by the technology can be resolved in a timely manner

Ensure accountability is clearly articulated and implemented throughout the system life cycle. | Considering all the elements of the ‘Accountable’ domain, please list key risks identified and planned mitigations / controls
Response: |
| Private

Stakeholder privacy and confidentiality is respected, and data is not used or stored beyond its intended and stated use and duration.

Data privacy: Data protection methods and principles are taken into account and data protection laws are complied with
Confidentiality: The technology can be trusted with protecting Deloitte, Client, and other confidential information and stored data

Ensure you understand your obligations regarding the data in scope both in training and production, how the technology uses your data and contractual terms withs vendors regarding such use of the data. 

Follow local Privacy/Confidentiality processes and refer to local privacy guidance, Data by Design guidance and Global Privacy Data Protection Considerations. [link] | Private

Stakeholder privacy and confidentiality is respected, and data is not used or stored beyond its intended and stated use and duration.

Data privacy: Data protection methods and principles are taken into account and data protection laws are complied with
Confidentiality: The technology can be trusted with protecting Deloitte, Client, and other confidential information and stored data

Ensure you understand your obligations regarding the data in scope both in training and production, how the technology uses your data and contractual terms withs vendors regarding such use of the data. 

Follow local Privacy/Confidentiality processes and refer to local privacy guidance, Data by Design guidance and Global Privacy Data Protection Considerations. [link] | Confirm Deloitte privacy or confidentiality processes or assessments have been initiated / completed and the outcome of any guidance received 

Response: |
| Private

Stakeholder privacy and confidentiality is respected, and data is not used or stored beyond its intended and stated use and duration.

Data privacy: Data protection methods and principles are taken into account and data protection laws are complied with
Confidentiality: The technology can be trusted with protecting Deloitte, Client, and other confidential information and stored data

Ensure you understand your obligations regarding the data in scope both in training and production, how the technology uses your data and contractual terms withs vendors regarding such use of the data. 

Follow local Privacy/Confidentiality processes and refer to local privacy guidance, Data by Design guidance and Global Privacy Data Protection Considerations. [link] | Private

Stakeholder privacy and confidentiality is respected, and data is not used or stored beyond its intended and stated use and duration.

Data privacy: Data protection methods and principles are taken into account and data protection laws are complied with
Confidentiality: The technology can be trusted with protecting Deloitte, Client, and other confidential information and stored data

Ensure you understand your obligations regarding the data in scope both in training and production, how the technology uses your data and contractual terms withs vendors regarding such use of the data. 

Follow local Privacy/Confidentiality processes and refer to local privacy guidance, Data by Design guidance and Global Privacy Data Protection Considerations. [link] | Does the system involve automated decision making with legal or similar effects on individuals or groups?

Response: |
| Private

Stakeholder privacy and confidentiality is respected, and data is not used or stored beyond its intended and stated use and duration.

Data privacy: Data protection methods and principles are taken into account and data protection laws are complied with
Confidentiality: The technology can be trusted with protecting Deloitte, Client, and other confidential information and stored data

Ensure you understand your obligations regarding the data in scope both in training and production, how the technology uses your data and contractual terms withs vendors regarding such use of the data. 

Follow local Privacy/Confidentiality processes and refer to local privacy guidance, Data by Design guidance and Global Privacy Data Protection Considerations. [link] | Private

Stakeholder privacy and confidentiality is respected, and data is not used or stored beyond its intended and stated use and duration.

Data privacy: Data protection methods and principles are taken into account and data protection laws are complied with
Confidentiality: The technology can be trusted with protecting Deloitte, Client, and other confidential information and stored data

Ensure you understand your obligations regarding the data in scope both in training and production, how the technology uses your data and contractual terms withs vendors regarding such use of the data. 

Follow local Privacy/Confidentiality processes and refer to local privacy guidance, Data by Design guidance and Global Privacy Data Protection Considerations. [link] | Considering all the elements of the ‘Private’ domain, please list key risks identified and planned mitigations/controls
Response: |
| Transparent and Explainable

Transparency and explainability are key to engendering trust – they are also critical to enabling stakeholders to fulfill other key requirements such as accountability and helping ensure compliance with laws, regulations and client obligations. Help stakeholders understand how their data can be used and interpreted and how Gen AI systems make decisions. These decisions and functioning of the system should be easy to understand, auditable, and open to inspection. 

Interpretable: Users can easily understand what the technology is doing and how it operates
Traceable: We can trace and document the data and processes that result in a decision by a technology and can clearly articulate the processes and reasoning of both the technology and any relevant adjoining human-led processes 
Visible: The technology’s purpose, use cases, logic, risks, and benefits are adequately communicated to and easily accessible by users. We are transparent regarding the data used by the technology and how it’s used. And – where relevant – it is clear to human users that they’re interacting with technology rather than a human
Justifiable: Outputs and decisions made using the technology are defensible
Ensure you understand and meet the transparency and explainability requirements appropriate to the system, data, and stakeholders. | Transparent and Explainable

Transparency and explainability are key to engendering trust – they are also critical to enabling stakeholders to fulfill other key requirements such as accountability and helping ensure compliance with laws, regulations and client obligations. Help stakeholders understand how their data can be used and interpreted and how Gen AI systems make decisions. These decisions and functioning of the system should be easy to understand, auditable, and open to inspection. 

Interpretable: Users can easily understand what the technology is doing and how it operates
Traceable: We can trace and document the data and processes that result in a decision by a technology and can clearly articulate the processes and reasoning of both the technology and any relevant adjoining human-led processes 
Visible: The technology’s purpose, use cases, logic, risks, and benefits are adequately communicated to and easily accessible by users. We are transparent regarding the data used by the technology and how it’s used. And – where relevant – it is clear to human users that they’re interacting with technology rather than a human
Justifiable: Outputs and decisions made using the technology are defensible
Ensure you understand and meet the transparency and explainability requirements appropriate to the system, data, and stakeholders. | Explain the measures taken to help ensure the applicable level of explainability and transparency are achieved for different groups of stakeholders.
Response: |
| Transparent and Explainable

Transparency and explainability are key to engendering trust – they are also critical to enabling stakeholders to fulfill other key requirements such as accountability and helping ensure compliance with laws, regulations and client obligations. Help stakeholders understand how their data can be used and interpreted and how Gen AI systems make decisions. These decisions and functioning of the system should be easy to understand, auditable, and open to inspection. 

Interpretable: Users can easily understand what the technology is doing and how it operates
Traceable: We can trace and document the data and processes that result in a decision by a technology and can clearly articulate the processes and reasoning of both the technology and any relevant adjoining human-led processes 
Visible: The technology’s purpose, use cases, logic, risks, and benefits are adequately communicated to and easily accessible by users. We are transparent regarding the data used by the technology and how it’s used. And – where relevant – it is clear to human users that they’re interacting with technology rather than a human
Justifiable: Outputs and decisions made using the technology are defensible
Ensure you understand and meet the transparency and explainability requirements appropriate to the system, data, and stakeholders. | Transparent and Explainable

Transparency and explainability are key to engendering trust – they are also critical to enabling stakeholders to fulfill other key requirements such as accountability and helping ensure compliance with laws, regulations and client obligations. Help stakeholders understand how their data can be used and interpreted and how Gen AI systems make decisions. These decisions and functioning of the system should be easy to understand, auditable, and open to inspection. 

Interpretable: Users can easily understand what the technology is doing and how it operates
Traceable: We can trace and document the data and processes that result in a decision by a technology and can clearly articulate the processes and reasoning of both the technology and any relevant adjoining human-led processes 
Visible: The technology’s purpose, use cases, logic, risks, and benefits are adequately communicated to and easily accessible by users. We are transparent regarding the data used by the technology and how it’s used. And – where relevant – it is clear to human users that they’re interacting with technology rather than a human
Justifiable: Outputs and decisions made using the technology are defensible
Ensure you understand and meet the transparency and explainability requirements appropriate to the system, data, and stakeholders. | Considering all of the elements of the ‘Transparent and Explainable’ domain, please list key risks identified and planned mitigations / controls
Response: |
| Fair and Impartial

Gen AI can embed discriminatory and harmful biases. The technology should be designed and operated inclusively for equitable application, access, and outcomes. It should account for group under- or over-representation. Assess whether Gen AI systems include internal and external checks to help enable equitable and inclusive application across all stakeholders.

Equitable: The outputs and recommendations of the technology promote equitability among all social identities (e.g., races, genders, etc.) and does not aim to disproportionately burden or provide more benefit for one group over another
Unbiased: We endeavor to eliminate and/or reduce unfair bias with social equality in mind, considering both the mathematical fairness of the data and decision-making process and whether individuals are treated fairly
Accessible: The technology is appropriately compatible with assistive technology, affordable, and equitably available

Ensure the system is designed and operated inclusively for equitable application, access, and outcomes. | Fair and Impartial

Gen AI can embed discriminatory and harmful biases. The technology should be designed and operated inclusively for equitable application, access, and outcomes. It should account for group under- or over-representation. Assess whether Gen AI systems include internal and external checks to help enable equitable and inclusive application across all stakeholders.

Equitable: The outputs and recommendations of the technology promote equitability among all social identities (e.g., races, genders, etc.) and does not aim to disproportionately burden or provide more benefit for one group over another
Unbiased: We endeavor to eliminate and/or reduce unfair bias with social equality in mind, considering both the mathematical fairness of the data and decision-making process and whether individuals are treated fairly
Accessible: The technology is appropriately compatible with assistive technology, affordable, and equitably available

Ensure the system is designed and operated inclusively for equitable application, access, and outcomes. | Relative to the risk that inequalities and / or unfair bias could exist in the Gen AI system or data sets (e.g., as a result of historical social bias, choice of data sets, the operation of the algorithms, the model evaluation or differences between live and training data) what tools and methods will be employed to seek to ensure fairness?
Response: |
| Fair and Impartial

Gen AI can embed discriminatory and harmful biases. The technology should be designed and operated inclusively for equitable application, access, and outcomes. It should account for group under- or over-representation. Assess whether Gen AI systems include internal and external checks to help enable equitable and inclusive application across all stakeholders.

Equitable: The outputs and recommendations of the technology promote equitability among all social identities (e.g., races, genders, etc.) and does not aim to disproportionately burden or provide more benefit for one group over another
Unbiased: We endeavor to eliminate and/or reduce unfair bias with social equality in mind, considering both the mathematical fairness of the data and decision-making process and whether individuals are treated fairly
Accessible: The technology is appropriately compatible with assistive technology, affordable, and equitably available

Ensure the system is designed and operated inclusively for equitable application, access, and outcomes. | Fair and Impartial

Gen AI can embed discriminatory and harmful biases. The technology should be designed and operated inclusively for equitable application, access, and outcomes. It should account for group under- or over-representation. Assess whether Gen AI systems include internal and external checks to help enable equitable and inclusive application across all stakeholders.

Equitable: The outputs and recommendations of the technology promote equitability among all social identities (e.g., races, genders, etc.) and does not aim to disproportionately burden or provide more benefit for one group over another
Unbiased: We endeavor to eliminate and/or reduce unfair bias with social equality in mind, considering both the mathematical fairness of the data and decision-making process and whether individuals are treated fairly
Accessible: The technology is appropriately compatible with assistive technology, affordable, and equitably available

Ensure the system is designed and operated inclusively for equitable application, access, and outcomes. | Throughout the lifecycle, how will you seek to ensure that the technology does not unevenly distribute benefits or harms across stakeholders or create short- or long-term harms for any groups, even if it is maximizing benefits for others? Could different demographic groups experience differences in quality of service?
Response: |
| Fair and Impartial

Gen AI can embed discriminatory and harmful biases. The technology should be designed and operated inclusively for equitable application, access, and outcomes. It should account for group under- or over-representation. Assess whether Gen AI systems include internal and external checks to help enable equitable and inclusive application across all stakeholders.

Equitable: The outputs and recommendations of the technology promote equitability among all social identities (e.g., races, genders, etc.) and does not aim to disproportionately burden or provide more benefit for one group over another
Unbiased: We endeavor to eliminate and/or reduce unfair bias with social equality in mind, considering both the mathematical fairness of the data and decision-making process and whether individuals are treated fairly
Accessible: The technology is appropriately compatible with assistive technology, affordable, and equitably available

Ensure the system is designed and operated inclusively for equitable application, access, and outcomes. | Fair and Impartial

Gen AI can embed discriminatory and harmful biases. The technology should be designed and operated inclusively for equitable application, access, and outcomes. It should account for group under- or over-representation. Assess whether Gen AI systems include internal and external checks to help enable equitable and inclusive application across all stakeholders.

Equitable: The outputs and recommendations of the technology promote equitability among all social identities (e.g., races, genders, etc.) and does not aim to disproportionately burden or provide more benefit for one group over another
Unbiased: We endeavor to eliminate and/or reduce unfair bias with social equality in mind, considering both the mathematical fairness of the data and decision-making process and whether individuals are treated fairly
Accessible: The technology is appropriately compatible with assistive technology, affordable, and equitably available

Ensure the system is designed and operated inclusively for equitable application, access, and outcomes. | Considering all the elements of the ‘Fair and Impartial’ domain, please list key risks identified and planned mitigations/controls
Response: |
| Robust and Reliable

Gen AI can have significant accuracy and performance concerns (e.g. "hallucination") and inability to quantify its uncertainty. Outputs should be consistent, accurate, and robust against disruptions or misuse. Effective quality and human in the loop measures are essential to ensuring the system is sufficiently robust and reliable, reducing the risks of adverse performance impacts throughout the lifecycle.

Adaptable: The technology is sufficiently agile and resilient to adjust to changing conditions
Accurate: The technology’s outputs can be trusted to be accurate and precise
Consistent: The technology and its algorithms can produce consistent results if re-run under the same conditions and with the same data 
Predictable: The technology, its algorithms, and its outputs are as expected, based on the patterns of the data
Data governance: We consider the integrity and suitability of the data associated with a technology

Ensure you document and demonstrate the measures taken to help ensure quality and deliver robust and reliable system operation throughout the lifecycle. | Robust and Reliable

Gen AI can have significant accuracy and performance concerns (e.g. "hallucination") and inability to quantify its uncertainty. Outputs should be consistent, accurate, and robust against disruptions or misuse. Effective quality and human in the loop measures are essential to ensuring the system is sufficiently robust and reliable, reducing the risks of adverse performance impacts throughout the lifecycle.

Adaptable: The technology is sufficiently agile and resilient to adjust to changing conditions
Accurate: The technology’s outputs can be trusted to be accurate and precise
Consistent: The technology and its algorithms can produce consistent results if re-run under the same conditions and with the same data 
Predictable: The technology, its algorithms, and its outputs are as expected, based on the patterns of the data
Data governance: We consider the integrity and suitability of the data associated with a technology

Ensure you document and demonstrate the measures taken to help ensure quality and deliver robust and reliable system operation throughout the lifecycle. | What tools and methods will be employed to monitor the robustness and reliability of the system?
Response: |
| Robust and Reliable

Gen AI can have significant accuracy and performance concerns (e.g. "hallucination") and inability to quantify its uncertainty. Outputs should be consistent, accurate, and robust against disruptions or misuse. Effective quality and human in the loop measures are essential to ensuring the system is sufficiently robust and reliable, reducing the risks of adverse performance impacts throughout the lifecycle.

Adaptable: The technology is sufficiently agile and resilient to adjust to changing conditions
Accurate: The technology’s outputs can be trusted to be accurate and precise
Consistent: The technology and its algorithms can produce consistent results if re-run under the same conditions and with the same data 
Predictable: The technology, its algorithms, and its outputs are as expected, based on the patterns of the data
Data governance: We consider the integrity and suitability of the data associated with a technology

Ensure you document and demonstrate the measures taken to help ensure quality and deliver robust and reliable system operation throughout the lifecycle. | Robust and Reliable

Gen AI can have significant accuracy and performance concerns (e.g. "hallucination") and inability to quantify its uncertainty. Outputs should be consistent, accurate, and robust against disruptions or misuse. Effective quality and human in the loop measures are essential to ensuring the system is sufficiently robust and reliable, reducing the risks of adverse performance impacts throughout the lifecycle.

Adaptable: The technology is sufficiently agile and resilient to adjust to changing conditions
Accurate: The technology’s outputs can be trusted to be accurate and precise
Consistent: The technology and its algorithms can produce consistent results if re-run under the same conditions and with the same data 
Predictable: The technology, its algorithms, and its outputs are as expected, based on the patterns of the data
Data governance: We consider the integrity and suitability of the data associated with a technology

Ensure you document and demonstrate the measures taken to help ensure quality and deliver robust and reliable system operation throughout the lifecycle. | Explain the quality and human in the loop measures / controls for this system, including the extent to which you are in control of the quality and data integrity of the data sources used?
Response: |
| Robust and Reliable

Gen AI can have significant accuracy and performance concerns (e.g. "hallucination") and inability to quantify its uncertainty. Outputs should be consistent, accurate, and robust against disruptions or misuse. Effective quality and human in the loop measures are essential to ensuring the system is sufficiently robust and reliable, reducing the risks of adverse performance impacts throughout the lifecycle.

Adaptable: The technology is sufficiently agile and resilient to adjust to changing conditions
Accurate: The technology’s outputs can be trusted to be accurate and precise
Consistent: The technology and its algorithms can produce consistent results if re-run under the same conditions and with the same data 
Predictable: The technology, its algorithms, and its outputs are as expected, based on the patterns of the data
Data governance: We consider the integrity and suitability of the data associated with a technology

Ensure you document and demonstrate the measures taken to help ensure quality and deliver robust and reliable system operation throughout the lifecycle. | Robust and Reliable

Gen AI can have significant accuracy and performance concerns (e.g. "hallucination") and inability to quantify its uncertainty. Outputs should be consistent, accurate, and robust against disruptions or misuse. Effective quality and human in the loop measures are essential to ensuring the system is sufficiently robust and reliable, reducing the risks of adverse performance impacts throughout the lifecycle.

Adaptable: The technology is sufficiently agile and resilient to adjust to changing conditions
Accurate: The technology’s outputs can be trusted to be accurate and precise
Consistent: The technology and its algorithms can produce consistent results if re-run under the same conditions and with the same data 
Predictable: The technology, its algorithms, and its outputs are as expected, based on the patterns of the data
Data governance: We consider the integrity and suitability of the data associated with a technology

Ensure you document and demonstrate the measures taken to help ensure quality and deliver robust and reliable system operation throughout the lifecycle. | Considering all the elements of the ‘Robust and Reliable’ domain, please list key risks identified and planned mitigations/controls
Response: |
| Safe and secure 

Gen AI can lead to harm, including misinformation and cybersecurity breaches. Risks of physical, psychological, environmental, and / or digital harm should be sufficiently mitigated. 

Security: The technology has checks and balances in place to help reduce risk and prevent internal / external attacks, conserving its safety, security, and functionality. We value technical security and resilience to reduce physical and digital harm 
Safety: The technology and its outputs do not create physical, mental, emotional, environmental, technological, individual and / or collective harm, regardless of intention. We implement relevant safety measures to govern a technology’s usage to protect people and society
User friendly: The technology is intuitive and does not mislead users

Ensure the system is aligned to relevant Global Technology Operating Model (GTOM) and cybersecurity standards and processes. 
Ensure potential safety / harms have been considered and mitigated where required. | Safe and secure 

Gen AI can lead to harm, including misinformation and cybersecurity breaches. Risks of physical, psychological, environmental, and / or digital harm should be sufficiently mitigated. 

Security: The technology has checks and balances in place to help reduce risk and prevent internal / external attacks, conserving its safety, security, and functionality. We value technical security and resilience to reduce physical and digital harm 
Safety: The technology and its outputs do not create physical, mental, emotional, environmental, technological, individual and / or collective harm, regardless of intention. We implement relevant safety measures to govern a technology’s usage to protect people and society
User friendly: The technology is intuitive and does not mislead users

Ensure the system is aligned to relevant Global Technology Operating Model (GTOM) and cybersecurity standards and processes. 
Ensure potential safety / harms have been considered and mitigated where required. | Confirm GTOM / Security processes and assessments (e.g. SSDLC) have been initiated / completed and outcome of any guidance received 
Response: |
| Safe and secure 

Gen AI can lead to harm, including misinformation and cybersecurity breaches. Risks of physical, psychological, environmental, and / or digital harm should be sufficiently mitigated. 

Security: The technology has checks and balances in place to help reduce risk and prevent internal / external attacks, conserving its safety, security, and functionality. We value technical security and resilience to reduce physical and digital harm 
Safety: The technology and its outputs do not create physical, mental, emotional, environmental, technological, individual and / or collective harm, regardless of intention. We implement relevant safety measures to govern a technology’s usage to protect people and society
User friendly: The technology is intuitive and does not mislead users

Ensure the system is aligned to relevant Global Technology Operating Model (GTOM) and cybersecurity standards and processes. 
Ensure potential safety / harms have been considered and mitigated where required. | Safe and secure 

Gen AI can lead to harm, including misinformation and cybersecurity breaches. Risks of physical, psychological, environmental, and / or digital harm should be sufficiently mitigated. 

Security: The technology has checks and balances in place to help reduce risk and prevent internal / external attacks, conserving its safety, security, and functionality. We value technical security and resilience to reduce physical and digital harm 
Safety: The technology and its outputs do not create physical, mental, emotional, environmental, technological, individual and / or collective harm, regardless of intention. We implement relevant safety measures to govern a technology’s usage to protect people and society
User friendly: The technology is intuitive and does not mislead users

Ensure the system is aligned to relevant Global Technology Operating Model (GTOM) and cybersecurity standards and processes. 
Ensure potential safety / harms have been considered and mitigated where required. | Is our data used or can it be accessed for any vendor purpose (e.g. foundation model training, service improvement, content / abuse monitoring)?
Response: |
| Safe and secure 

Gen AI can lead to harm, including misinformation and cybersecurity breaches. Risks of physical, psychological, environmental, and / or digital harm should be sufficiently mitigated. 

Security: The technology has checks and balances in place to help reduce risk and prevent internal / external attacks, conserving its safety, security, and functionality. We value technical security and resilience to reduce physical and digital harm 
Safety: The technology and its outputs do not create physical, mental, emotional, environmental, technological, individual and / or collective harm, regardless of intention. We implement relevant safety measures to govern a technology’s usage to protect people and society
User friendly: The technology is intuitive and does not mislead users

Ensure the system is aligned to relevant Global Technology Operating Model (GTOM) and cybersecurity standards and processes. 
Ensure potential safety / harms have been considered and mitigated where required. | Safe and secure 

Gen AI can lead to harm, including misinformation and cybersecurity breaches. Risks of physical, psychological, environmental, and / or digital harm should be sufficiently mitigated. 

Security: The technology has checks and balances in place to help reduce risk and prevent internal / external attacks, conserving its safety, security, and functionality. We value technical security and resilience to reduce physical and digital harm 
Safety: The technology and its outputs do not create physical, mental, emotional, environmental, technological, individual and / or collective harm, regardless of intention. We implement relevant safety measures to govern a technology’s usage to protect people and society
User friendly: The technology is intuitive and does not mislead users

Ensure the system is aligned to relevant Global Technology Operating Model (GTOM) and cybersecurity standards and processes. 
Ensure potential safety / harms have been considered and mitigated where required. | Considering all the elements of the ‘Safe and Secure’ domain, please list key risks identified and planned mitigations/controls
Response: |
| Responsible 

The technology should be created and operated in a socially responsible manner, aligned to the organization's ethical values and environmental and societal objectives [link to local guidance]

Autonomous: The technology respects and supports human autonomy and decision making and allows for choice and freedom from control and / or influence
Value Adding: The technology is created with value creation in mind. Considering the risks that technology introduces, it is important to consider the unique benefits of the technology and its use case(s) to determine if appropriate to develop / implement 
Sustainability focused: The technology supports economic, environmental, and social sustainability throughout its full lifecycle
Common/Social Good: We choose to work with, develop, and implement technologies that help us to make a positive impact on society and we strive to both protect those most vulnerable from harm and create systems that benefit them
Well-being: We consider the human impact of technology, and believe that technology should seek to encourage well-being

Ensure the system has been evaluated with sufficient diversity of perspective to help ensure responsible design, use, outputs and impact on individuals, society and the environment. | Responsible 

The technology should be created and operated in a socially responsible manner, aligned to the organization's ethical values and environmental and societal objectives [link to local guidance]

Autonomous: The technology respects and supports human autonomy and decision making and allows for choice and freedom from control and / or influence
Value Adding: The technology is created with value creation in mind. Considering the risks that technology introduces, it is important to consider the unique benefits of the technology and its use case(s) to determine if appropriate to develop / implement 
Sustainability focused: The technology supports economic, environmental, and social sustainability throughout its full lifecycle
Common/Social Good: We choose to work with, develop, and implement technologies that help us to make a positive impact on society and we strive to both protect those most vulnerable from harm and create systems that benefit them
Well-being: We consider the human impact of technology, and believe that technology should seek to encourage well-being

Ensure the system has been evaluated with sufficient diversity of perspective to help ensure responsible design, use, outputs and impact on individuals, society and the environment. | Does the use case involve activity in the High Risk tier as defined in the EU AI Act or other AI regulations? [link]
Response: |
| Responsible 

The technology should be created and operated in a socially responsible manner, aligned to the organization's ethical values and environmental and societal objectives [link to local guidance]

Autonomous: The technology respects and supports human autonomy and decision making and allows for choice and freedom from control and / or influence
Value Adding: The technology is created with value creation in mind. Considering the risks that technology introduces, it is important to consider the unique benefits of the technology and its use case(s) to determine if appropriate to develop / implement 
Sustainability focused: The technology supports economic, environmental, and social sustainability throughout its full lifecycle
Common/Social Good: We choose to work with, develop, and implement technologies that help us to make a positive impact on society and we strive to both protect those most vulnerable from harm and create systems that benefit them
Well-being: We consider the human impact of technology, and believe that technology should seek to encourage well-being

Ensure the system has been evaluated with sufficient diversity of perspective to help ensure responsible design, use, outputs and impact on individuals, society and the environment. | Responsible 

The technology should be created and operated in a socially responsible manner, aligned to the organization's ethical values and environmental and societal objectives [link to local guidance]

Autonomous: The technology respects and supports human autonomy and decision making and allows for choice and freedom from control and / or influence
Value Adding: The technology is created with value creation in mind. Considering the risks that technology introduces, it is important to consider the unique benefits of the technology and its use case(s) to determine if appropriate to develop / implement 
Sustainability focused: The technology supports economic, environmental, and social sustainability throughout its full lifecycle
Common/Social Good: We choose to work with, develop, and implement technologies that help us to make a positive impact on society and we strive to both protect those most vulnerable from harm and create systems that benefit them
Well-being: We consider the human impact of technology, and believe that technology should seek to encourage well-being

Ensure the system has been evaluated with sufficient diversity of perspective to help ensure responsible design, use, outputs and impact on individuals, society and the environment. | Is the system designed, implemented, and used in a way that is responsible in terms of impact on the business, the stakeholders, end users, wider society, and the environment?
Response: |
| Responsible 

The technology should be created and operated in a socially responsible manner, aligned to the organization's ethical values and environmental and societal objectives [link to local guidance]

Autonomous: The technology respects and supports human autonomy and decision making and allows for choice and freedom from control and / or influence
Value Adding: The technology is created with value creation in mind. Considering the risks that technology introduces, it is important to consider the unique benefits of the technology and its use case(s) to determine if appropriate to develop / implement 
Sustainability focused: The technology supports economic, environmental, and social sustainability throughout its full lifecycle
Common/Social Good: We choose to work with, develop, and implement technologies that help us to make a positive impact on society and we strive to both protect those most vulnerable from harm and create systems that benefit them
Well-being: We consider the human impact of technology, and believe that technology should seek to encourage well-being

Ensure the system has been evaluated with sufficient diversity of perspective to help ensure responsible design, use, outputs and impact on individuals, society and the environment. | Responsible 

The technology should be created and operated in a socially responsible manner, aligned to the organization's ethical values and environmental and societal objectives [link to local guidance]

Autonomous: The technology respects and supports human autonomy and decision making and allows for choice and freedom from control and / or influence
Value Adding: The technology is created with value creation in mind. Considering the risks that technology introduces, it is important to consider the unique benefits of the technology and its use case(s) to determine if appropriate to develop / implement 
Sustainability focused: The technology supports economic, environmental, and social sustainability throughout its full lifecycle
Common/Social Good: We choose to work with, develop, and implement technologies that help us to make a positive impact on society and we strive to both protect those most vulnerable from harm and create systems that benefit them
Well-being: We consider the human impact of technology, and believe that technology should seek to encourage well-being

Ensure the system has been evaluated with sufficient diversity of perspective to help ensure responsible design, use, outputs and impact on individuals, society and the environment. | Could use of the Gen AI system impact
The legal position or life opportunities of individuals (e.g., credit, education, employment, healthcare, housing, insurance, benefits, services etc (or the terms on which they're provided)), or
Human rights: if the solution implementation or outcomes may lead to a loss of, fundamental change to, or decisions about, individuals’ employment, this should be highlighted as a relevant impact.
Response: |
| Responsible 

The technology should be created and operated in a socially responsible manner, aligned to the organization's ethical values and environmental and societal objectives [link to local guidance]

Autonomous: The technology respects and supports human autonomy and decision making and allows for choice and freedom from control and / or influence
Value Adding: The technology is created with value creation in mind. Considering the risks that technology introduces, it is important to consider the unique benefits of the technology and its use case(s) to determine if appropriate to develop / implement 
Sustainability focused: The technology supports economic, environmental, and social sustainability throughout its full lifecycle
Common/Social Good: We choose to work with, develop, and implement technologies that help us to make a positive impact on society and we strive to both protect those most vulnerable from harm and create systems that benefit them
Well-being: We consider the human impact of technology, and believe that technology should seek to encourage well-being

Ensure the system has been evaluated with sufficient diversity of perspective to help ensure responsible design, use, outputs and impact on individuals, society and the environment. | Responsible 

The technology should be created and operated in a socially responsible manner, aligned to the organization's ethical values and environmental and societal objectives [link to local guidance]

Autonomous: The technology respects and supports human autonomy and decision making and allows for choice and freedom from control and / or influence
Value Adding: The technology is created with value creation in mind. Considering the risks that technology introduces, it is important to consider the unique benefits of the technology and its use case(s) to determine if appropriate to develop / implement 
Sustainability focused: The technology supports economic, environmental, and social sustainability throughout its full lifecycle
Common/Social Good: We choose to work with, develop, and implement technologies that help us to make a positive impact on society and we strive to both protect those most vulnerable from harm and create systems that benefit them
Well-being: We consider the human impact of technology, and believe that technology should seek to encourage well-being

Ensure the system has been evaluated with sufficient diversity of perspective to help ensure responsible design, use, outputs and impact on individuals, society and the environment. | Considering all the elements of the ‘Responsible’ domain, please list key risks identified and planned mitigations / controls
Response: |
