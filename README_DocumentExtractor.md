# 文件內容提取器 (Document Extractor)

## 📋 功能說明

這個 Python 腳本可以將 PDF、DOCX、PPTX 文件轉換為 Markdown 格式，讓 AI 助手能夠讀取和分析文件內容。

### 支援格式
- **PDF**: 提取文字內容，保留頁面結構
- **DOCX**: 提取文字、標題、表格等結構化內容
- **PPTX**: 提取投影片標題和內容

### 輸出格式
- 統一轉換為 Markdown (.md) 格式
- 保留原始文件的基本結構
- 檔名格式: `原檔名_extracted.md`

## 🚀 快速開始

### 1. 安裝依賴套件

#### Windows 用戶
```batch
# 執行批次檔安裝
install_dependencies.bat

# 或執行 PowerShell 腳本
powershell -ExecutionPolicy Bypass -File install_dependencies.ps1
```

#### 手動安裝
```bash
pip install PyPDF2 pdfplumber python-docx python-pptx
```

### 2. 使用方法

#### 基本用法
```bash
# 處理當前目錄所有支援的文件
python document_extractor.py

# 檢查依賴套件安裝狀況
python document_extractor.py --check
```

#### 進階用法
```bash
# 指定輸入目錄
python document_extractor.py -i /path/to/documents

# 指定輸出目錄
python document_extractor.py -o /path/to/output

# 同時指定輸入和輸出目錄
python document_extractor.py -i ./documents -o ./extracted_content
```

## 📁 文件結構

```
project/
├── document_extractor.py          # 主程式
├── install_dependencies.bat       # Windows 安裝腳本
├── install_dependencies.ps1       # PowerShell 安裝腳本
├── README_DocumentExtractor.md    # 使用說明
├── documents/                     # 原始文件目錄
│   ├── policy.pdf
│   ├── guidelines.docx
│   └── presentation.pptx
└── extracted/                     # 提取結果目錄
    ├── policy_extracted.md
    ├── guidelines_extracted.md
    ├── presentation_extracted.md
    └── extraction_report.md       # 處理報告
```

## 🔧 依賴套件說明

| 套件名稱 | 用途 | 必要性 |
|---------|------|--------|
| PyPDF2 | PDF 文件基本處理 | PDF 支援必需 |
| pdfplumber | 增強的 PDF 文字提取 | PDF 支援必需 |
| python-docx | DOCX 文件處理 | DOCX 支援必需 |
| python-pptx | PPTX 文件處理 | PPTX 支援必需 |

**注意**: 如果某個套件未安裝，對應格式的文件將無法處理，但不影響其他格式的處理。

## 📊 輸出範例

### PDF 提取結果
```markdown
# 政策文件

**文件類型**: PDF
**提取時間**: 2024-12-20 10:30:00

## 第 1 頁

這是第一頁的內容...

## 第 2 頁

這是第二頁的內容...
```

### DOCX 提取結果
```markdown
# 使用指南

**文件類型**: DOCX
**提取時間**: 2024-12-20 10:30:00

## 第一章 概述

這是第一章的內容...

### 1.1 目的

這是子章節的內容...

## 表格內容

### 表格 1

| 項目 | 說明 | 狀態 |
| --- | --- | --- |
| 項目1 | 說明1 | 完成 |
| 項目2 | 說明2 | 進行中 |
```

### PPTX 提取結果
```markdown
# 簡報標題

**文件類型**: PPTX
**提取時間**: 2024-12-20 10:30:00

## 投影片 1

簡報標題
副標題內容

## 投影片 2

章節標題
• 重點一
• 重點二
• 重點三
```

## ⚠️ 注意事項

### 文件限制
- **受保護的 PDF**: 需要密碼的 PDF 文件無法處理
- **圖片內容**: 無法提取圖片中的文字（OCR 功能未包含）
- **複雜格式**: 某些複雜的格式可能無法完美保留
- **文件損壞**: 損壞的文件會跳過處理

### 效能考量
- **大型文件**: 處理大型文件可能需要較長時間
- **記憶體使用**: 同時處理多個大文件可能消耗較多記憶體
- **磁碟空間**: 確保輸出目錄有足夠的空間

## 🐛 故障排除

### 常見問題

#### 1. 套件安裝失敗
```bash
# 升級 pip
python -m pip install --upgrade pip

# 使用國內鏡像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ PyPDF2
```

#### 2. 編碼錯誤
確保系統支援 UTF-8 編碼：
```bash
# Windows
chcp 65001

# 設定環境變數
set PYTHONIOENCODING=utf-8
```

#### 3. 權限問題
```bash
# 使用管理員權限執行
# 或安裝到用戶目錄
pip install --user PyPDF2
```

#### 4. PDF 提取失敗
- 嘗試使用不同的 PDF 閱讀器重新儲存文件
- 檢查 PDF 是否受密碼保護
- 確認 PDF 文件未損壞

### 日誌檔案
程式執行時會產生 `document_extractor.log` 日誌檔案，包含詳細的處理資訊和錯誤訊息。

## 🔄 更新與擴展

### 新增支援格式
要新增其他文件格式支援，可以：

1. 安裝對應的處理套件
2. 在 `DocumentExtractor` 類別中新增提取方法
3. 更新 `supported_formats` 列表

### 自訂輸出格式
可以修改各個 `extract_*` 方法來自訂輸出格式，例如：
- 新增更多 Markdown 格式化
- 包含更多元數據
- 自訂章節結構

## 📞 技術支援

如果遇到問題，請檢查：

1. **日誌檔案**: `document_extractor.log`
2. **依賴套件**: 執行 `python document_extractor.py --check`
3. **Python 版本**: 建議使用 Python 3.7+
4. **文件格式**: 確認文件未損壞且格式正確

## 📄 授權說明

本工具為開源軟體，可自由使用和修改。使用時請遵守相關套件的授權條款。

---

**版本**: 1.0  
**更新日期**: 2024-12-20  
**作者**: AI Assistant
