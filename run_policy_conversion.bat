@echo off
chcp 65001 >nul
title 政策文件轉換自動化工具

echo.
echo ========================================
echo 🚀 政策文件轉換自動化工具
echo ========================================
echo.
echo 此工具將自動執行以下步驟:
echo 1. 創建 "ref Policy" 資料夾
echo 2. 創建 "md Policy" 資料夾  
echo 3. 安裝必要的 Python 套件
echo 4. 移動政策文件到 "ref Policy"
echo 5. 轉換文件為 Markdown 格式
echo 6. 移動轉換結果到 "md Policy"
echo.

echo 📋 使用說明:
echo - 請將所有政策文件 (PDF, DOCX, PPTX) 放在當前目錄
echo - 轉換完成後，原始文件會移動到 "ref Policy" 資料夾
echo - 轉換後的 Markdown 文件會放在 "md Policy" 資料夾
echo.

set /p confirm="是否繼續執行? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo 取消執行
    pause
    exit /b 0
)

echo.
echo ⏳ 正在啟動轉換流程...
echo.

REM 檢查 Python 是否安裝
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 錯誤: 未找到 Python
    echo 請先安裝 Python 3.7+ 
    echo 下載地址: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

REM 檢查主程式是否存在
if not exist "setup_policy_conversion.py" (
    echo ❌ 錯誤: 找不到 setup_policy_conversion.py
    echo 請確認所有程式文件都在當前目錄中
    echo.
    pause
    exit /b 1
)

if not exist "document_extractor.py" (
    echo ❌ 錯誤: 找不到 document_extractor.py
    echo 請確認所有程式文件都在當前目錄中
    echo.
    pause
    exit /b 1
)

REM 執行主程式
echo 🔄 執行政策文件轉換...
python setup_policy_conversion.py

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo ✅ 轉換完成！
    echo ========================================
    echo.
    echo 📁 結果位置:
    echo   - 原始文件: "ref Policy" 資料夾
    echo   - 轉換結果: "md Policy" 資料夾
    echo   - 摘要報告: conversion_summary.md
    echo   - 詳細日誌: policy_conversion.log
    echo.
    echo 💡 建議下一步:
    echo 1. 檢查 "md Policy" 資料夾中的轉換結果
    echo 2. 查看摘要報告了解轉換統計
    echo 3. 使用轉換後的文件更新 AI 政策
    echo.
) else (
    echo.
    echo ========================================
    echo ❌ 轉換失敗
    echo ========================================
    echo.
    echo 請檢查以下項目:
    echo 1. 查看 policy_conversion.log 了解錯誤詳情
    echo 2. 確認政策文件格式正確且未損壞
    echo 3. 檢查網路連線 (安裝套件時需要)
    echo 4. 確認有足夠的磁碟空間
    echo.
)

echo 按任意鍵退出...
pause >nul
