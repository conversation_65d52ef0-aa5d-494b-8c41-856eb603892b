# 政策文件轉換自動化工具

## 🎯 功能概述

這個自動化工具可以一鍵完成政策文件的整理和轉換流程：

1. **自動創建資料夾結構**
   - `ref Policy` - 存放原始政策文件
   - `md Policy` - 存放轉換後的 Markdown 文件

2. **自動安裝依賴套件**
   - PyPDF2, pdfplumber (PDF 處理)
   - python-docx (DOCX 處理)  
   - python-pptx (PPTX 處理)

3. **自動文件轉換**
   - 支援 PDF、DOCX、PPTX 格式
   - 轉換為結構化的 Markdown 格式
   - 保留文件的基本結構和內容

## 📁 最終目錄結構

```
專案目錄/
├── ref Policy/                    # 原始政策文件
│   ├── Draft_AI_DPM_5Sept2024.docx
│   ├── AP_Generative_AI_QRM_Guidelines.pdf
│   └── 生成式人工智能使用指南.pdf
├── md Policy/                     # 轉換後的 Markdown 文件
│   ├── Draft_AI_DPM_5Sept2024_extracted.md
│   ├── AP_Generative_AI_QRM_Guidelines_extracted.md
│   └── 生成式人工智能使用指南_extracted.md
├── setup_policy_conversion.py     # 主控程式
├── document_extractor.py          # 文件提取器
├── run_policy_conversion.bat      # Windows 執行腳本
├── run_policy_conversion.ps1      # PowerShell 執行腳本
├── conversion_summary.md          # 轉換摘要報告
└── policy_conversion.log          # 詳細執行日誌
```

## 🚀 快速開始

### 方法一：使用批次檔 (推薦)

1. **準備文件**
   ```
   將所有政策文件 (PDF, DOCX, PPTX) 放在當前目錄
   ```

2. **執行轉換**
   ```batch
   # 雙擊執行或在命令列執行
   run_policy_conversion.bat
   ```

3. **查看結果**
   - 原始文件 → `ref Policy` 資料夾
   - 轉換結果 → `md Policy` 資料夾

### 方法二：使用 PowerShell

```powershell
# 執行 PowerShell 腳本
powershell -ExecutionPolicy Bypass -File run_policy_conversion.ps1
```

### 方法三：手動執行

```bash
# 直接執行 Python 程式
python setup_policy_conversion.py
```

## 📋 執行流程詳解

### 第一階段：環境準備
1. ✅ 檢查 Python 環境
2. ✅ 創建 `ref Policy` 資料夾
3. ✅ 創建 `md Policy` 資料夾
4. ✅ 安裝依賴套件

### 第二階段：文件整理
1. 🔍 掃描當前目錄中的政策文件
2. 📁 移動文件到 `ref Policy` 資料夾
3. 📝 記錄移動的文件清單

### 第三階段：文件轉換
1. 🔄 執行 `document_extractor.py`
2. 📄 提取 PDF、DOCX、PPTX 內容
3. 📝 轉換為 Markdown 格式

### 第四階段：結果整理
1. 📁 移動轉換結果到 `md Policy` 資料夾
2. 🧹 清理臨時文件
3. 📊 生成摘要報告

## 📊 輸出文件說明

### 轉換後的 Markdown 文件
每個原始文件會產生對應的 `_extracted.md` 文件：

```markdown
# 文件標題

**文件類型**: PDF/DOCX/PPTX
**提取時間**: 2024-12-20 10:30:00

## 第一章 內容

文件的實際內容...

## 表格內容 (僅 DOCX)

| 欄位1 | 欄位2 | 欄位3 |
|-------|-------|-------|
| 資料1 | 資料2 | 資料3 |
```

### 摘要報告 (conversion_summary.md)
包含轉換統計和文件清單：

- 📁 目錄結構概覽
- 📄 原始文件清單
- 📝 轉換結果清單
- 📊 轉換成功率統計

### 執行日誌 (policy_conversion.log)
詳細記錄整個執行過程：

- 🕐 時間戳記
- ℹ️ 執行步驟
- ✅ 成功訊息
- ⚠️ 警告訊息
- ❌ 錯誤詳情

## ⚠️ 注意事項

### 系統需求
- **Python 3.7+** (必須)
- **網路連線** (安裝套件時需要)
- **足夠磁碟空間** (建議至少 100MB)

### 文件限制
- **受保護的 PDF**: 需要密碼的文件無法處理
- **損壞的文件**: 會自動跳過並記錄
- **大型文件**: 可能需要較長處理時間

### 檔名注意事項
- 避免使用特殊字元
- 建議使用英文或數字命名
- 如果檔名重複，會自動添加時間戳

## 🔧 故障排除

### 常見問題

#### 1. Python 未安裝
```
錯誤: 未找到 Python
解決: 下載並安裝 Python 3.7+
網址: https://www.python.org/downloads/
```

#### 2. 套件安裝失敗
```bash
# 升級 pip
python -m pip install --upgrade pip

# 使用國內鏡像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ PyPDF2
```

#### 3. 權限問題
```bash
# Windows: 以管理員身分執行
# 或安裝到用戶目錄
pip install --user PyPDF2
```

#### 4. 編碼錯誤
```batch
# Windows 命令列
chcp 65001
set PYTHONIOENCODING=utf-8
```

### 檢查清單

執行前請確認：
- [ ] Python 已正確安裝
- [ ] 政策文件已放在當前目錄
- [ ] 有足夠的磁碟空間
- [ ] 網路連線正常

執行後請檢查：
- [ ] `ref Policy` 資料夾包含原始文件
- [ ] `md Policy` 資料夾包含轉換結果
- [ ] `conversion_summary.md` 顯示成功統計
- [ ] 沒有錯誤訊息在日誌中

## 🎯 使用建議

### 最佳實務
1. **備份重要文件** - 執行前先備份原始文件
2. **分批處理** - 大量文件建議分批處理
3. **檢查結果** - 轉換後檢查 Markdown 文件品質
4. **保留日誌** - 保存日誌文件以供後續參考

### 後續步驟
1. **檢查轉換品質** - 開啟 `md Policy` 中的文件檢查
2. **更新 AI 政策** - 使用轉換結果更新 `TW_AI_Policy.md`
3. **更新參考來源** - 修正 `reference.md` 中的參考文獻
4. **版本控制** - 將結果加入版本控制系統

## 📞 技術支援

如果遇到問題：

1. **查看日誌** - 檢查 `policy_conversion.log`
2. **檢查摘要** - 查看 `conversion_summary.md`
3. **重新執行** - 嘗試重新執行轉換流程
4. **手動處理** - 對失敗的文件進行手動轉換

---

**版本**: 1.0  
**更新日期**: 2024-12-20  
**作者**: AI Assistant
