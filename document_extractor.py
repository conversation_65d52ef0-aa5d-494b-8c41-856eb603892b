#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件內容提取器
將 PDF、DOCX、PPTX 文件轉換為 Markdown 格式，供 AI 助手讀取

支援格式：
- PDF: 提取文字內容，保留基本結構
- DOCX: 提取文字、標題、表格等結構化內容  
- PPTX: 提取投影片標題和內容

作者: AI Assistant
版本: 1.0
"""

import os
import sys
import logging
from pathlib import Path
from typing import List, Dict, Optional
import argparse
from datetime import datetime

# 第三方套件導入
try:
    import PyPDF2
    import pdfplumber
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

try:
    from pptx import Presentation
    PPTX_AVAILABLE = True
except ImportError:
    PPTX_AVAILABLE = False

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('document_extractor.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DocumentExtractor:
    """文件內容提取器主類別"""
    
    def __init__(self, input_dir: str = ".", output_dir: str = "./extracted"):
        """
        初始化提取器
        
        Args:
            input_dir: 輸入目錄路徑
            output_dir: 輸出目錄路徑
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 支援的文件格式
        self.supported_formats = []
        if PDF_AVAILABLE:
            self.supported_formats.extend(['.pdf'])
        if DOCX_AVAILABLE:
            self.supported_formats.extend(['.docx'])
        if PPTX_AVAILABLE:
            self.supported_formats.extend(['.pptx'])
            
        logger.info(f"支援的格式: {', '.join(self.supported_formats)}")
    
    def check_dependencies(self) -> Dict[str, bool]:
        """檢查依賴套件是否已安裝"""
        deps = {
            'PyPDF2/pdfplumber': PDF_AVAILABLE,
            'python-docx': DOCX_AVAILABLE,
            'python-pptx': PPTX_AVAILABLE
        }
        
        logger.info("依賴套件檢查:")
        for dep, available in deps.items():
            status = "✓ 已安裝" if available else "✗ 未安裝"
            logger.info(f"  {dep}: {status}")
            
        return deps
    
    def find_documents(self) -> List[Path]:
        """掃描目錄中的支援文件"""
        documents = []
        
        for file_path in self.input_dir.rglob("*"):
            if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                documents.append(file_path)
                
        logger.info(f"找到 {len(documents)} 個支援的文件")
        return documents
    
    def extract_pdf(self, file_path: Path) -> str:
        """提取 PDF 文件內容"""
        if not PDF_AVAILABLE:
            return "錯誤: PDF 處理套件未安裝"
            
        content = []
        content.append(f"# {file_path.stem}")
        content.append(f"**文件類型**: PDF")
        content.append(f"**提取時間**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        content.append("")
        
        try:
            # 嘗試使用 pdfplumber (更好的文字提取)
            import pdfplumber
            with pdfplumber.open(file_path) as pdf:
                for i, page in enumerate(pdf.pages, 1):
                    text = page.extract_text()
                    if text and text.strip():
                        content.append(f"## 第 {i} 頁")
                        content.append("")
                        content.append(text.strip())
                        content.append("")
                        
        except Exception as e:
            logger.warning(f"pdfplumber 失敗，嘗試 PyPDF2: {e}")
            try:
                # 備用方案：使用 PyPDF2
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for i, page in enumerate(pdf_reader.pages, 1):
                        text = page.extract_text()
                        if text and text.strip():
                            content.append(f"## 第 {i} 頁")
                            content.append("")
                            content.append(text.strip())
                            content.append("")
            except Exception as e2:
                content.append(f"錯誤: 無法提取 PDF 內容 - {e2}")
                
        return "\n".join(content)
    
    def extract_docx(self, file_path: Path) -> str:
        """提取 DOCX 文件內容"""
        if not DOCX_AVAILABLE:
            return "錯誤: DOCX 處理套件未安裝"
            
        content = []
        content.append(f"# {file_path.stem}")
        content.append(f"**文件類型**: DOCX")
        content.append(f"**提取時間**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        content.append("")
        
        try:
            doc = Document(file_path)
            
            # 提取段落內容
            for para in doc.paragraphs:
                text = para.text.strip()
                if text:
                    # 根據樣式判斷標題層級
                    if para.style.name.startswith('Heading'):
                        level = para.style.name.replace('Heading ', '')
                        if level.isdigit():
                            content.append(f"{'#' * int(level)} {text}")
                        else:
                            content.append(f"## {text}")
                    else:
                        content.append(text)
                    content.append("")
            
            # 提取表格內容
            if doc.tables:
                content.append("## 表格內容")
                content.append("")
                
                for i, table in enumerate(doc.tables, 1):
                    content.append(f"### 表格 {i}")
                    content.append("")
                    
                    # 表格標題行
                    if table.rows:
                        header_row = table.rows[0]
                        headers = [cell.text.strip() for cell in header_row.cells]
                        content.append("| " + " | ".join(headers) + " |")
                        content.append("| " + " | ".join(["---"] * len(headers)) + " |")
                        
                        # 表格數據行
                        for row in table.rows[1:]:
                            cells = [cell.text.strip() for cell in row.cells]
                            content.append("| " + " | ".join(cells) + " |")
                    
                    content.append("")
                    
        except Exception as e:
            content.append(f"錯誤: 無法提取 DOCX 內容 - {e}")
            
        return "\n".join(content)
    
    def extract_pptx(self, file_path: Path) -> str:
        """提取 PPTX 文件內容"""
        if not PPTX_AVAILABLE:
            return "錯誤: PPTX 處理套件未安裝"
            
        content = []
        content.append(f"# {file_path.stem}")
        content.append(f"**文件類型**: PPTX")
        content.append(f"**提取時間**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        content.append("")
        
        try:
            prs = Presentation(file_path)
            
            for i, slide in enumerate(prs.slides, 1):
                content.append(f"## 投影片 {i}")
                content.append("")
                
                # 提取投影片中的所有文字
                slide_text = []
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_text.append(shape.text.strip())
                
                if slide_text:
                    content.extend(slide_text)
                else:
                    content.append("*此投影片無文字內容*")
                
                content.append("")
                
        except Exception as e:
            content.append(f"錯誤: 無法提取 PPTX 內容 - {e}")
            
        return "\n".join(content)
    
    def extract_document(self, file_path: Path) -> Optional[str]:
        """根據文件類型提取內容"""
        suffix = file_path.suffix.lower()
        
        logger.info(f"正在處理: {file_path.name}")
        
        try:
            if suffix == '.pdf':
                return self.extract_pdf(file_path)
            elif suffix == '.docx':
                return self.extract_docx(file_path)
            elif suffix == '.pptx':
                return self.extract_pptx(file_path)
            else:
                logger.warning(f"不支援的文件格式: {suffix}")
                return None
                
        except Exception as e:
            logger.error(f"處理文件 {file_path.name} 時發生錯誤: {e}")
            return None
    
    def save_extracted_content(self, file_path: Path, content: str) -> Path:
        """儲存提取的內容"""
        output_filename = f"{file_path.stem}_extracted.md"
        output_path = self.output_dir / output_filename
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"已儲存: {output_path}")
            return output_path
        except Exception as e:
            logger.error(f"儲存文件時發生錯誤: {e}")
            raise
    
    def process_all(self) -> Dict[str, List[str]]:
        """處理所有支援的文件"""
        documents = self.find_documents()
        results = {
            'success': [],
            'failed': [],
            'skipped': []
        }
        
        if not documents:
            logger.warning("未找到支援的文件")
            return results
        
        for doc_path in documents:
            try:
                content = self.extract_document(doc_path)
                if content:
                    output_path = self.save_extracted_content(doc_path, content)
                    results['success'].append(str(output_path))
                else:
                    results['skipped'].append(str(doc_path))
            except Exception as e:
                logger.error(f"處理 {doc_path} 失敗: {e}")
                results['failed'].append(str(doc_path))
        
        # 輸出處理結果摘要
        logger.info("處理完成!")
        logger.info(f"成功: {len(results['success'])} 個文件")
        logger.info(f"失敗: {len(results['failed'])} 個文件")
        logger.info(f"跳過: {len(results['skipped'])} 個文件")
        
        return results

def print_installation_guide():
    """列印安裝指南"""
    print("\n" + "="*60)
    print("📦 依賴套件安裝指南")
    print("="*60)
    print("\n請執行以下命令安裝所需套件:")
    print("\n# 基本安裝")
    print("pip install PyPDF2 pdfplumber python-docx python-pptx")
    print("\n# 或分別安裝")
    print("pip install PyPDF2        # PDF 處理")
    print("pip install pdfplumber    # 更好的 PDF 文字提取")
    print("pip install python-docx   # DOCX 處理")
    print("pip install python-pptx   # PPTX 處理")
    print("\n" + "="*60)

def main():
    """主函數"""
    parser = argparse.ArgumentParser(description='文件內容提取器')
    parser.add_argument('--input', '-i', default='.', help='輸入目錄 (預設: 當前目錄)')
    parser.add_argument('--output', '-o', default='./extracted', help='輸出目錄 (預設: ./extracted)')
    parser.add_argument('--check', '-c', action='store_true', help='僅檢查依賴套件')
    
    args = parser.parse_args()
    
    # 建立提取器實例
    extractor = DocumentExtractor(args.input, args.output)
    
    # 檢查依賴套件
    deps = extractor.check_dependencies()
    
    if args.check:
        print_installation_guide()
        return
    
    # 檢查是否有可用的套件
    if not any(deps.values()):
        logger.error("沒有安裝任何支援的套件!")
        print_installation_guide()
        return
    
    # 處理文件
    try:
        results = extractor.process_all()
        
        # 生成處理報告
        report_path = extractor.output_dir / "extraction_report.md"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 文件提取報告\n\n")
            f.write(f"**處理時間**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## 成功處理的文件\n\n")
            for file in results['success']:
                f.write(f"- {file}\n")
            
            f.write("\n## 處理失敗的文件\n\n")
            for file in results['failed']:
                f.write(f"- {file}\n")
                
            f.write("\n## 跳過的文件\n\n")
            for file in results['skipped']:
                f.write(f"- {file}\n")
        
        logger.info(f"處理報告已儲存至: {report_path}")
        
    except KeyboardInterrupt:
        logger.info("使用者中斷處理")
    except Exception as e:
        logger.error(f"處理過程中發生錯誤: {e}")

if __name__ == "__main__":
    main()
