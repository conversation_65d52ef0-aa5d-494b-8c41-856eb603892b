# 政策文件轉換自動化工具 (PowerShell)
# 編碼: UTF-8

$Host.UI.RawUI.WindowTitle = "政策文件轉換自動化工具"

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🚀 政策文件轉換自動化工具" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "此工具將自動執行以下步驟:" -ForegroundColor Yellow
Write-Host "1. 創建 'ref Policy' 資料夾" -ForegroundColor White
Write-Host "2. 創建 'md Policy' 資料夾" -ForegroundColor White
Write-Host "3. 安裝必要的 Python 套件" -ForegroundColor White
Write-Host "4. 移動政策文件到 'ref Policy'" -ForegroundColor White
Write-Host "5. 轉換文件為 Markdown 格式" -ForegroundColor White
Write-Host "6. 移動轉換結果到 'md Policy'" -ForegroundColor White
Write-Host ""

Write-Host "📋 使用說明:" -ForegroundColor Cyan
Write-Host "- 請將所有政策文件 (PDF, DOCX, PPTX) 放在當前目錄" -ForegroundColor Gray
Write-Host "- 轉換完成後，原始文件會移動到 'ref Policy' 資料夾" -ForegroundColor Gray
Write-Host "- 轉換後的 Markdown 文件會放在 'md Policy' 資料夾" -ForegroundColor Gray
Write-Host ""

# 確認執行
$confirm = Read-Host "是否繼續執行? (Y/N)"
if ($confirm -notmatch '^[Yy]$') {
    Write-Host "取消執行" -ForegroundColor Yellow
    Read-Host "按 Enter 鍵退出"
    exit 0
}

Write-Host ""
Write-Host "⏳ 正在啟動轉換流程..." -ForegroundColor Yellow
Write-Host ""

# 檢查 Python 是否安裝
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✓ Python 環境: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 錯誤: 未找到 Python" -ForegroundColor Red
    Write-Host "請先安裝 Python 3.7+" -ForegroundColor Yellow
    Write-Host "下載地址: https://www.python.org/downloads/" -ForegroundColor Cyan
    Write-Host ""
    Read-Host "按 Enter 鍵退出"
    exit 1
}

# 檢查主程式是否存在
$requiredFiles = @("setup_policy_conversion.py", "document_extractor.py")
$missingFiles = @()

foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        $missingFiles += $file
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host "❌ 錯誤: 找不到以下必要文件:" -ForegroundColor Red
    foreach ($file in $missingFiles) {
        Write-Host "  - $file" -ForegroundColor Red
    }
    Write-Host "請確認所有程式文件都在當前目錄中" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "按 Enter 鍵退出"
    exit 1
}

# 執行主程式
Write-Host "🔄 執行政策文件轉換..." -ForegroundColor Cyan

try {
    $process = Start-Process -FilePath "python" -ArgumentList "setup_policy_conversion.py" -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "✅ 轉換完成！" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host ""
        
        Write-Host "📁 結果位置:" -ForegroundColor Cyan
        Write-Host "  - 原始文件: 'ref Policy' 資料夾" -ForegroundColor White
        Write-Host "  - 轉換結果: 'md Policy' 資料夾" -ForegroundColor White
        Write-Host "  - 摘要報告: conversion_summary.md" -ForegroundColor White
        Write-Host "  - 詳細日誌: policy_conversion.log" -ForegroundColor White
        Write-Host ""
        
        Write-Host "💡 建議下一步:" -ForegroundColor Yellow
        Write-Host "1. 檢查 'md Policy' 資料夾中的轉換結果" -ForegroundColor Gray
        Write-Host "2. 查看摘要報告了解轉換統計" -ForegroundColor Gray
        Write-Host "3. 使用轉換後的文件更新 AI 政策" -ForegroundColor Gray
        Write-Host ""
        
        # 詢問是否開啟結果資料夾
        $openFolder = Read-Host "是否開啟 'md Policy' 資料夾查看結果? (Y/N)"
        if ($openFolder -match '^[Yy]$') {
            if (Test-Path "md Policy") {
                Invoke-Item "md Policy"
            }
        }
        
    } else {
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Red
        Write-Host "❌ 轉換失敗" -ForegroundColor Red
        Write-Host "========================================" -ForegroundColor Red
        Write-Host ""
        
        Write-Host "請檢查以下項目:" -ForegroundColor Yellow
        Write-Host "1. 查看 policy_conversion.log 了解錯誤詳情" -ForegroundColor Gray
        Write-Host "2. 確認政策文件格式正確且未損壞" -ForegroundColor Gray
        Write-Host "3. 檢查網路連線 (安裝套件時需要)" -ForegroundColor Gray
        Write-Host "4. 確認有足夠的磁碟空間" -ForegroundColor Gray
        Write-Host ""
        
        # 詢問是否開啟日誌文件
        $openLog = Read-Host "是否開啟日誌文件查看錯誤詳情? (Y/N)"
        if ($openLog -match '^[Yy]$') {
            if (Test-Path "policy_conversion.log") {
                Invoke-Item "policy_conversion.log"
            }
        }
    }
    
} catch {
    Write-Host ""
    Write-Host "❌ 執行過程中發生錯誤: $_" -ForegroundColor Red
    Write-Host ""
}

Write-Host ""
Read-Host "按 Enter 鍵退出"
