@echo off
chcp 65001 >nul
echo ========================================
echo 文件提取器依賴套件安裝腳本
echo ========================================
echo.

echo 正在檢查 Python 環境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [錯誤] 未找到 Python，請先安裝 Python 3.7+
    echo 下載地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python 環境檢查通過
echo.

echo 正在安裝依賴套件...
echo.

echo [1/4] 安裝 PyPDF2...
pip install PyPDF2
if %errorlevel% neq 0 (
    echo [警告] PyPDF2 安裝失敗
) else (
    echo [成功] PyPDF2 安裝完成
)
echo.

echo [2/4] 安裝 pdfplumber...
pip install pdfplumber
if %errorlevel% neq 0 (
    echo [警告] pdfplumber 安裝失敗
) else (
    echo [成功] pdfplumber 安裝完成
)
echo.

echo [3/4] 安裝 python-docx...
pip install python-docx
if %errorlevel% neq 0 (
    echo [警告] python-docx 安裝失敗
) else (
    echo [成功] python-docx 安裝完成
)
echo.

echo [4/4] 安裝 python-pptx...
pip install python-pptx
if %errorlevel% neq 0 (
    echo [警告] python-pptx 安裝失敗
) else (
    echo [成功] python-pptx 安裝完成
)
echo.

echo ========================================
echo 安裝完成！
echo ========================================
echo.

echo 測試安裝結果...
python document_extractor.py --check

echo.
echo 使用方法:
echo   python document_extractor.py                    # 處理當前目錄所有文件
echo   python document_extractor.py -i 輸入目錄        # 指定輸入目錄
echo   python document_extractor.py -o 輸出目錄        # 指定輸出目錄
echo   python document_extractor.py --check            # 檢查依賴套件
echo.

pause
