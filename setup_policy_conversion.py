#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
政策文件轉換自動化部署腳本
自動創建資料夾結構、部署程式、轉換文件

流程:
1. 創建 "ref Policy" 資料夾 - 存放原始政策文件
2. 創建 "md Policy" 資料夾 - 存放轉換後的 Markdown 文件
3. 自動安裝依賴套件
4. 移動原始文件到 ref Policy
5. 執行文件轉換
6. 移動轉換結果到 md Policy

作者: AI Assistant
版本: 1.0
"""

import os
import sys
import shutil
import subprocess
import logging
from pathlib import Path
from datetime import datetime
import glob

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('policy_conversion.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class PolicyConversionManager:
    """政策文件轉換管理器"""
    
    def __init__(self, base_dir="."):
        """初始化管理器"""
        self.base_dir = Path(base_dir)
        self.ref_policy_dir = self.base_dir / "ref Policy"
        self.md_policy_dir = self.base_dir / "md Policy"
        self.temp_extracted_dir = self.base_dir / "extracted"
        
        # 支援的文件格式
        self.supported_formats = ['.pdf', '.docx', '.pptx']
        
        logger.info("政策文件轉換管理器初始化完成")
        logger.info(f"基礎目錄: {self.base_dir.absolute()}")
        logger.info(f"參考政策目錄: {self.ref_policy_dir}")
        logger.info(f"Markdown 政策目錄: {self.md_policy_dir}")
    
    def create_directory_structure(self):
        """創建目錄結構"""
        logger.info("正在創建目錄結構...")
        
        try:
            # 創建 ref Policy 目錄
            self.ref_policy_dir.mkdir(exist_ok=True)
            logger.info(f"✓ 創建目錄: {self.ref_policy_dir}")
            
            # 創建 md Policy 目錄
            self.md_policy_dir.mkdir(exist_ok=True)
            logger.info(f"✓ 創建目錄: {self.md_policy_dir}")
            
            # 創建臨時提取目錄
            self.temp_extracted_dir.mkdir(exist_ok=True)
            logger.info(f"✓ 創建臨時目錄: {self.temp_extracted_dir}")
            
            return True
            
        except Exception as e:
            logger.error(f"創建目錄結構失敗: {e}")
            return False
    
    def check_python_environment(self):
        """檢查 Python 環境"""
        logger.info("檢查 Python 環境...")
        
        try:
            result = subprocess.run([sys.executable, '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"✓ Python 環境: {result.stdout.strip()}")
                return True
            else:
                logger.error("Python 環境檢查失敗")
                return False
        except Exception as e:
            logger.error(f"Python 環境檢查錯誤: {e}")
            return False
    
    def install_dependencies(self):
        """安裝依賴套件"""
        logger.info("正在安裝依賴套件...")
        
        packages = ['PyPDF2', 'pdfplumber', 'python-docx', 'python-pptx']
        success_count = 0
        
        for package in packages:
            try:
                logger.info(f"安裝 {package}...")
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', package
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info(f"✓ {package} 安裝成功")
                    success_count += 1
                else:
                    logger.warning(f"✗ {package} 安裝失敗: {result.stderr}")
                    
            except Exception as e:
                logger.warning(f"✗ {package} 安裝錯誤: {e}")
        
        logger.info(f"依賴套件安裝完成: {success_count}/{len(packages)} 成功")
        return success_count > 0
    
    def find_policy_files(self):
        """尋找當前目錄中的政策文件"""
        logger.info("正在掃描政策文件...")
        
        policy_files = []
        
        for format_ext in self.supported_formats:
            pattern = f"*{format_ext}"
            files = list(self.base_dir.glob(pattern))
            policy_files.extend(files)
        
        # 過濾掉已經在 ref Policy 目錄中的文件
        policy_files = [f for f in policy_files if not str(f).startswith(str(self.ref_policy_dir))]
        
        logger.info(f"找到 {len(policy_files)} 個政策文件:")
        for file in policy_files:
            logger.info(f"  - {file.name}")
        
        return policy_files
    
    def move_files_to_ref_policy(self, files):
        """移動文件到 ref Policy 目錄"""
        logger.info("正在移動文件到 ref Policy 目錄...")
        
        moved_files = []
        
        for file in files:
            try:
                dest_path = self.ref_policy_dir / file.name
                
                # 如果目標文件已存在，添加時間戳
                if dest_path.exists():
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    stem = dest_path.stem
                    suffix = dest_path.suffix
                    dest_path = self.ref_policy_dir / f"{stem}_{timestamp}{suffix}"
                
                shutil.move(str(file), str(dest_path))
                logger.info(f"✓ 移動: {file.name} -> {dest_path.name}")
                moved_files.append(dest_path)
                
            except Exception as e:
                logger.error(f"✗ 移動文件失敗 {file.name}: {e}")
        
        logger.info(f"文件移動完成: {len(moved_files)}/{len(files)} 成功")
        return moved_files
    
    def run_document_extractor(self):
        """執行文件提取器"""
        logger.info("正在執行文件提取...")
        
        try:
            # 檢查 document_extractor.py 是否存在
            extractor_path = self.base_dir / "document_extractor.py"
            if not extractor_path.exists():
                logger.error("document_extractor.py 不存在，請確認文件已正確部署")
                return False
            
            # 執行文件提取器
            cmd = [
                sys.executable, 
                str(extractor_path),
                '-i', str(self.ref_policy_dir),
                '-o', str(self.temp_extracted_dir)
            ]
            
            logger.info(f"執行命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✓ 文件提取完成")
                logger.info("提取器輸出:")
                for line in result.stdout.split('\n'):
                    if line.strip():
                        logger.info(f"  {line}")
                return True
            else:
                logger.error("✗ 文件提取失敗")
                logger.error(f"錯誤輸出: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"執行文件提取器時發生錯誤: {e}")
            return False
    
    def move_extracted_to_md_policy(self):
        """移動提取的文件到 md Policy 目錄"""
        logger.info("正在移動提取的文件到 md Policy 目錄...")
        
        try:
            # 尋找所有 .md 文件
            md_files = list(self.temp_extracted_dir.glob("*.md"))
            
            moved_count = 0
            for md_file in md_files:
                try:
                    dest_path = self.md_policy_dir / md_file.name
                    
                    # 如果目標文件已存在，添加時間戳
                    if dest_path.exists():
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        stem = dest_path.stem
                        suffix = dest_path.suffix
                        dest_path = self.md_policy_dir / f"{stem}_{timestamp}{suffix}"
                    
                    shutil.move(str(md_file), str(dest_path))
                    logger.info(f"✓ 移動: {md_file.name} -> {dest_path.name}")
                    moved_count += 1
                    
                except Exception as e:
                    logger.error(f"✗ 移動文件失敗 {md_file.name}: {e}")
            
            logger.info(f"Markdown 文件移動完成: {moved_count} 個文件")
            
            # 清理臨時目錄
            if self.temp_extracted_dir.exists():
                shutil.rmtree(self.temp_extracted_dir)
                logger.info("✓ 清理臨時目錄")
            
            return moved_count > 0
            
        except Exception as e:
            logger.error(f"移動提取文件時發生錯誤: {e}")
            return False
    
    def generate_summary_report(self):
        """生成摘要報告"""
        logger.info("正在生成摘要報告...")
        
        try:
            # 統計文件數量
            ref_files = list(self.ref_policy_dir.glob("*"))
            md_files = list(self.md_policy_dir.glob("*.md"))
            
            # 生成報告內容
            report_content = f"""# 政策文件轉換摘要報告

**轉換時間**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📁 目錄結構

```
{self.base_dir.name}/
├── ref Policy/          # 原始政策文件 ({len(ref_files)} 個文件)
├── md Policy/           # 轉換後的 Markdown 文件 ({len(md_files)} 個文件)
├── document_extractor.py
└── policy_conversion.log
```

## 📄 原始政策文件 (ref Policy)

"""
            
            for file in ref_files:
                if file.is_file():
                    size_mb = file.stat().st_size / (1024 * 1024)
                    report_content += f"- **{file.name}** ({size_mb:.2f} MB)\n"
            
            report_content += f"""
## 📝 轉換後的 Markdown 文件 (md Policy)

"""
            
            for file in md_files:
                if file.is_file():
                    size_kb = file.stat().st_size / 1024
                    report_content += f"- **{file.name}** ({size_kb:.1f} KB)\n"
            
            report_content += f"""

## 📊 轉換統計

- **原始文件數量**: {len(ref_files)} 個
- **轉換成功數量**: {len(md_files)} 個
- **轉換成功率**: {(len(md_files)/max(len(ref_files), 1)*100):.1f}%

## 🎯 下一步建議

1. 檢查 `md Policy` 目錄中的轉換結果
2. 使用轉換後的 Markdown 文件更新 AI 政策
3. 參考 `policy_conversion.log` 查看詳細處理記錄

---
*此報告由政策文件轉換管理器自動生成*
"""
            
            # 儲存報告
            report_path = self.base_dir / "conversion_summary.md"
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            logger.info(f"✓ 摘要報告已儲存: {report_path}")
            return True
            
        except Exception as e:
            logger.error(f"生成摘要報告失敗: {e}")
            return False
    
    def run_full_conversion(self):
        """執行完整的轉換流程"""
        logger.info("="*60)
        logger.info("開始政策文件轉換自動化流程")
        logger.info("="*60)
        
        steps = [
            ("檢查 Python 環境", self.check_python_environment),
            ("創建目錄結構", self.create_directory_structure),
            ("安裝依賴套件", self.install_dependencies),
        ]
        
        # 執行前置步驟
        for step_name, step_func in steps:
            logger.info(f"\n🔄 步驟: {step_name}")
            if not step_func():
                logger.error(f"❌ 步驟失敗: {step_name}")
                return False
            logger.info(f"✅ 步驟完成: {step_name}")
        
        # 尋找並移動政策文件
        logger.info(f"\n🔄 步驟: 移動政策文件到 ref Policy")
        policy_files = self.find_policy_files()
        if policy_files:
            moved_files = self.move_files_to_ref_policy(policy_files)
            if not moved_files:
                logger.warning("沒有文件被移動到 ref Policy 目錄")
        else:
            logger.info("當前目錄中沒有找到政策文件，跳過移動步驟")
        
        # 執行文件轉換
        logger.info(f"\n🔄 步驟: 執行文件轉換")
        if not self.run_document_extractor():
            logger.error("❌ 文件轉換失敗")
            return False
        logger.info(f"✅ 步驟完成: 文件轉換")
        
        # 移動轉換結果
        logger.info(f"\n🔄 步驟: 移動轉換結果到 md Policy")
        if not self.move_extracted_to_md_policy():
            logger.error("❌ 移動轉換結果失敗")
            return False
        logger.info(f"✅ 步驟完成: 移動轉換結果")
        
        # 生成摘要報告
        logger.info(f"\n🔄 步驟: 生成摘要報告")
        if not self.generate_summary_report():
            logger.warning("⚠️ 生成摘要報告失敗，但不影響主流程")
        else:
            logger.info(f"✅ 步驟完成: 生成摘要報告")
        
        logger.info("\n" + "="*60)
        logger.info("🎉 政策文件轉換自動化流程完成！")
        logger.info("="*60)
        logger.info(f"📁 原始文件位置: {self.ref_policy_dir}")
        logger.info(f"📝 轉換結果位置: {self.md_policy_dir}")
        logger.info(f"📊 摘要報告: conversion_summary.md")
        logger.info(f"📋 詳細日誌: policy_conversion.log")
        
        return True

def main():
    """主函數"""
    print("🚀 政策文件轉換自動化部署工具")
    print("="*50)
    
    try:
        # 創建管理器並執行轉換
        manager = PolicyConversionManager()
        success = manager.run_full_conversion()
        
        if success:
            print("\n✅ 轉換流程執行成功！")
            print("\n📋 後續步驟:")
            print("1. 檢查 'md Policy' 目錄中的轉換結果")
            print("2. 查看 'conversion_summary.md' 了解轉換統計")
            print("3. 使用轉換後的文件更新 AI 政策")
        else:
            print("\n❌ 轉換流程執行失敗，請查看日誌文件")
            
    except KeyboardInterrupt:
        print("\n⏹️ 使用者中斷執行")
    except Exception as e:
        print(f"\n💥 執行過程中發生未預期的錯誤: {e}")
        logger.error(f"主程式錯誤: {e}")

if __name__ == "__main__":
    main()
