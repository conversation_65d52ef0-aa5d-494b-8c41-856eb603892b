# 文件提取器依賴套件安裝腳本 (PowerShell)
# 編碼: UTF-8

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "文件提取器依賴套件安裝腳本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 檢查 Python 環境
Write-Host "正在檢查 Python 環境..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Python 環境檢查通過: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "[錯誤] 未找到 Python，請先安裝 Python 3.7+" -ForegroundColor Red
    Write-Host "下載地址: https://www.python.org/downloads/" -ForegroundColor Yellow
    Read-Host "按 Enter 鍵退出"
    exit 1
}

Write-Host ""

# 安裝依賴套件
$packages = @(
    @{Name="PyPDF2"; Description="PDF 文件處理"},
    @{Name="pdfplumber"; Description="增強的 PDF 文字提取"},
    @{Name="python-docx"; Description="DOCX 文件處理"},
    @{Name="python-pptx"; Description="PPTX 文件處理"}
)

$successCount = 0
$totalCount = $packages.Count

Write-Host "正在安裝依賴套件..." -ForegroundColor Yellow
Write-Host ""

for ($i = 0; $i -lt $packages.Count; $i++) {
    $package = $packages[$i]
    $progress = $i + 1
    
    Write-Host "[$progress/$totalCount] 安裝 $($package.Name) ($($package.Description))..." -ForegroundColor Cyan
    
    try {
        $result = pip install $package.Name 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "[成功] $($package.Name) 安裝完成" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "[警告] $($package.Name) 安裝失敗" -ForegroundColor Yellow
            Write-Host "錯誤詳情: $result" -ForegroundColor Red
        }
    } catch {
        Write-Host "[警告] $($package.Name) 安裝失敗" -ForegroundColor Yellow
        Write-Host "錯誤詳情: $_" -ForegroundColor Red
    }
    Write-Host ""
}

# 顯示安裝結果
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "安裝完成！" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "安裝結果: $successCount/$totalCount 個套件安裝成功" -ForegroundColor $(if ($successCount -eq $totalCount) { "Green" } else { "Yellow" })
Write-Host ""

# 測試安裝結果
Write-Host "測試安裝結果..." -ForegroundColor Yellow
try {
    python document_extractor.py --check
} catch {
    Write-Host "無法執行測試，請檢查 document_extractor.py 是否存在" -ForegroundColor Red
}

Write-Host ""
Write-Host "使用方法:" -ForegroundColor Cyan
Write-Host "  python document_extractor.py                    # 處理當前目錄所有文件" -ForegroundColor White
Write-Host "  python document_extractor.py -i 輸入目錄        # 指定輸入目錄" -ForegroundColor White
Write-Host "  python document_extractor.py -o 輸出目錄        # 指定輸出目錄" -ForegroundColor White
Write-Host "  python document_extractor.py --check            # 檢查依賴套件" -ForegroundColor White
Write-Host ""

# 創建快捷執行腳本
$shortcutScript = @"
@echo off
chcp 65001 >nul
echo 正在提取文件內容...
python document_extractor.py
echo.
echo 處理完成！提取的內容已儲存在 extracted 目錄中
pause
"@

try {
    $shortcutScript | Out-File -FilePath "run_extractor.bat" -Encoding ASCII
    Write-Host "已創建快捷執行腳本: run_extractor.bat" -ForegroundColor Green
} catch {
    Write-Host "創建快捷執行腳本失敗: $_" -ForegroundColor Yellow
}

Write-Host ""
Read-Host "按 Enter 鍵退出"
